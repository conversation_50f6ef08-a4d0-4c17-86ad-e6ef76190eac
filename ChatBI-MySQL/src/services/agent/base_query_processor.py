"""
统一的查询处理器基类
提取runner.py的核心功能，为API和飞书处理提供统一的基础架构
"""
import asyncio
import json
import queue
import concurrent.futures
import weakref
import threading
from datetime import datetime
from typing import List, Dict, Generator, Optional, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod

from agents import Runner, RunConfig, ModelSettings
from src.models.user_info_class import UserInfo
from src.services.agent.bots.coordinator_bot import CoordinatorBot
from src.services.agent.utils.formatter import format_event_message
from src.services.agent.utils.model_provider import LITE_LLM_MODEL
from src.services.chatbot.history_service import (
    save_assistant_message,
    get_conversation_history_as_input_list
)
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email
from src.utils.image_utils import process_images_for_ai

# 领域常量
KNOWN_AGENTS = {
    "sales_order_analytics",
    "warehouse_and_fulfillment", 
    "general_chat_bot"
}

MAX_RETRIES = 3
QUEUE_TIMEOUT = 600
THREAD_POOL_SIZE = 20


class BackgroundTaskManager:
    """后台任务管理器 - 处理中断后的任务继续执行"""

    def __init__(self, max_workers=10):
        self.task_queue = queue.Queue()
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="background_worker"
        )
        self.active_tasks = weakref.WeakSet()
        self._start_workers()

    def _start_workers(self):
        """启动后台工作线程"""
        for _ in range(3):  # 启动3个工作线程
            future = self.executor.submit(self._worker)
            self.active_tasks.add(future)

    def _worker(self):
        """后台工作线程"""
        while True:
            try:
                task = self.task_queue.get(timeout=30)  # 30秒超时
                if task is None:
                    break

                # 处理任务
                self._process_task(task)

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Background worker error: {e}")
            finally:
                self.task_queue.task_done()

    def _process_task(self, task):
        """处理后台任务"""
        try:
            processor = task.get("processor")
            request = task.get("request")

            if processor and request:
                # 创建消息队列并执行任务
                message_queue = queue.Queue()
                async_worker = processor.create_async_worker(request, message_queue)

                # 执行任务并消费结果
                async_worker()

                logger.info(f"后台任务完成 (Convo ID: {request.conversation_id})")

        except Exception as e:
            logger.error(f"Background task processing error: {e}")

    def submit_task(self, task):
        """提交后台任务"""
        self.task_queue.put(task)

    def shutdown(self):
        """优雅关闭后台任务管理器"""
        # 停止工作线程
        for _ in range(3):
            self.task_queue.put(None)

        # 关闭线程池
        self.executor.shutdown(wait=True)


# 全局线程池
THREAD_POOL = concurrent.futures.ThreadPoolExecutor(
    max_workers=THREAD_POOL_SIZE,
    thread_name_prefix="agent_worker"
)

# 全局后台任务管理器
BACKGROUND_MANAGER = BackgroundTaskManager()


@dataclass
class QueryRequest:
    """查询请求领域模型"""
    user_query: str
    user_info: Dict[str, Any]
    access_token: Optional[str] = None
    conversation_id: Optional[str] = None
    images: Optional[List[str]] = None
    model_name: Optional[str] = None


@dataclass
class QueryResult:
    """查询结果领域模型"""
    response: str
    logs: str
    timestamp: int
    final_input_list: Optional[List[Dict]] = None
    bot_instance: Optional[CoordinatorBot] = None
    used_agents: Optional[List[str]] = None
    interrupted: bool = False


@dataclass
class StreamEvent:
    """流事件领域模型"""
    event_type: str
    content: str
    data: Optional[Any] = None


class BaseQueryProcessor(ABC):
    """统一的查询处理器基类"""
    
    def __init__(self, thread_pool: concurrent.futures.ThreadPoolExecutor):
        self.thread_pool = thread_pool
        
    def create_user_info_object(self, user_info: dict, access_token: str = None, conversation_id: str = None) -> UserInfo:
        """创建UserInfo对象"""
        user_name = user_info.get("name")
        email = get_valid_user_email(user_info)
        union_id = user_info.get("union_id")
        summerfarm_api_token = user_info.get("summerfarm_api_token")
        
        return UserInfo(
            user_name=user_name,
            email=email,
            access_token=access_token,
            union_id=union_id,
            summerfarm_api_token=summerfarm_api_token,
            open_id=user_info.get("open_id", ""),
            conversation_id=conversation_id
        )
    
    def get_conversation_history(self, user_info: dict, conversation_id: str) -> List[Dict]:
        """获取对话历史"""
        if not conversation_id:
            return []
            
        user_name = user_info.get("name")
        email = get_valid_user_email(user_info)
        
        history = get_conversation_history_as_input_list(
            user_name, email, conversation_id
        )
        logger.info(f"获取到 {len(history)} 条历史消息")
        return history
    
    def build_messages(self, user_query: str, images: List[str] = None, history: List[Dict] = None) -> List[Dict]:
        """构建消息列表，支持多模态"""
        if history is None:
            history = []
            
        if images and len(images) > 0:
            logger.info(f"开始处理 {len(images)} 张图片，下载并转换为base64编码")
            
            # 下载图片并转换为base64编码
            processed_images = process_images_for_ai(images)
            
            if processed_images:
                # 使用正确的多模态消息格式
                content_parts = [{"type": "input_text", "text": user_query}]
                
                # 添加处理后的图片内容
                for base64_image in processed_images:
                    content_parts.append({
                        "type": "input_image",
                        "image_url": base64_image,
                        "detail": "auto"
                    })
                
                user_message = {
                    "role": "user",
                    "content": content_parts
                }
                logger.info(f"用户消息包含 {len(processed_images)} 张已处理的图片，使用多模态格式")
            else:
                # 如果所有图片都处理失败，回退到纯文本消息
                logger.warning("所有图片处理失败，回退到纯文本消息")
                user_message = {"role": "user", "content": user_query}
        else:
            # 纯文本消息
            user_message = {"role": "user", "content": user_query}
        
        return history + [user_message]
    
    def extract_agent_name_from_tool_output(self, tool_output: str) -> str:
        """从工具输出中提取Agent名称"""
        try:
            # 处理None或空输入
            if not tool_output:
                return "专业工具"

            logger.info(f"从工具输出提取Agent名称: {tool_output[:200]}...")

            # 尝试解析JSON格式的工具输出
            if tool_output.strip().startswith('{'):
                data = json.loads(tool_output)

                # 处理results数组的情况
                if "results" in data and isinstance(data["results"], list):
                    agent_names = []
                    for result in data["results"]:
                        if isinstance(result, dict) and "agent_name" in result and result.get("success", False):
                            agent_names.append(result["agent_name"])
                    if agent_names:
                        return ",".join(agent_names)

                # 处理单个agent_name的情况
                agent_name = data.get("agent_name")
                if agent_name:
                    return agent_name

            # 尝试从工具输出中匹配已知的Agent名称模式
            tool_output_lower = tool_output.lower()
            matched_agents = []
            for agent in KNOWN_AGENTS:
                if agent in tool_output_lower:
                    matched_agents.append(agent)

            if matched_agents:
                return ",".join(matched_agents)

            # 如果包含"analysis"字样，说明是分析工具
            if "analysis" in tool_output_lower:
                return "专业分析工具"

            # 默认返回通用名称
            return "专业工具"

        except Exception as e:
            logger.exception(f"提取Agent名称时出错: {e}")
            return "专业工具"
    
    async def process_stream_events(self, result, message_queue: queue.Queue, user_obj: UserInfo) -> tuple:
        """统一的流事件处理逻辑"""
        collected_logs = ""
        tool_calls_detected = False
        agent_execution_logs = []
        used_agent_names = []

        try:
            async for event in result.stream_events():
                message = format_event_message(event)
                if message:
                    msg_type = message.get("type")
                    content = message.get("content", "")

                    # 收集所有相关日志
                    if msg_type in ["log", "handoff_log", "tool_output"] and content:
                        collected_logs += str(content) + "\n"

                    # 检测工具调用（Agent as Tool架构的关键指标）
                    if msg_type == "tool_output" and content:
                        tool_calls_detected = True
                        # 尝试解析工具调用结果，提取Agent名称
                        agent_name = self.extract_agent_name_from_tool_output(content)
                        if agent_name:
                            # 收集使用的agent名称
                            if agent_name not in used_agent_names:
                                used_agent_names.append(agent_name)

                            agent_log = f"🔄 CoordinatorBot调用专业工具: {agent_name}"
                            agent_execution_logs.append(agent_log)
                            logger.info(agent_log)
                            # 发送类似handoff的日志消息，保持UI一致性
                            message_queue.put({
                                "type": "handoff_log",
                                "content": agent_log
                            })

                    # 检测工具调用日志（从tool_call_log中提取agent名称）
                    if msg_type == "tool_call_log" and content:
                        agent_name = self._extract_agent_name_from_tool_call(content)
                        if agent_name and agent_name in KNOWN_AGENTS:
                            if agent_name not in used_agent_names:
                                tool_calls_detected = True
                                used_agent_names.append(agent_name)

                            agent_log = f"🔄 CoordinatorBot调用专业工具: {agent_name}"
                            agent_execution_logs.append(agent_log)
                            logger.info(agent_log)

                            # 发送类似handoff的日志消息
                            message_queue.put({
                                "type": "handoff_log",
                                "content": agent_log
                            })

                    message_queue.put(message)

        except Exception as e:
            logger.exception(f"流事件处理出错: {e}")
            raise

        return collected_logs, tool_calls_detected, agent_execution_logs, used_agent_names

    def _extract_agent_name_from_tool_call(self, tool_call_content: str) -> str:
        """从工具调用内容中提取Agent名称"""
        try:
            # 尝试从工具调用内容中匹配已知的Agent名称
            tool_call_lower = tool_call_content.lower()
            for agent in KNOWN_AGENTS:
                if agent in tool_call_lower:
                    return agent
            return None
        except Exception as e:
            logger.warning(f"提取工具调用Agent名称时出错: {e}")
            return None
    
    @abstractmethod
    def handle_message_output(self, message: dict) -> str:
        """处理消息输出 - 子类需要实现具体的输出格式"""
        pass
    
    def save_assistant_response(self, user_info: dict, conversation_id: str, response: str, 
                              logs: str, timestamp: int, final_input_list: List[Dict] = None,
                              used_agents: List[str] = None, bot_instance: CoordinatorBot = None):
        """保存助手回复到历史记录"""
        if not (response or logs):
            return
            
        try:
            user_name = user_info.get("name")
            email = get_valid_user_email(user_info)
            
            structured_message = None
            if final_input_list and isinstance(final_input_list, list) and len(final_input_list) > 0:
                if (final_input_list and
                    isinstance(final_input_list[-1], dict) and
                    final_input_list[-1].get("role") == "assistant"):
                    structured_message = final_input_list[-1]
            
            output_as_input_json = None
            if structured_message:
                try:
                    output_as_input_json = json.dumps(
                        structured_message,
                        ensure_ascii=False,
                        indent=2
                    )
                except Exception as e:
                    logger.error(f"序列化结构化消息失败: {e}")
            
            if conversation_id and timestamp:
                # 获取使用的agent信息 - 统一的Agent追踪机制
                used_agents_str = None
                if used_agents and len(used_agents) > 0:
                    # 优先使用从流事件中提取的agent名称
                    used_agents_str = ",".join(used_agents)
                    logger.info(f"本次查询使用的agent: {used_agents_str}")
                elif bot_instance and hasattr(bot_instance, 'tool_registry'):
                    # 从AgentToolRegistry获取
                    used_agents_str = bot_instance.tool_registry.get_used_agents_string()
                    logger.info(f"从tool_registry获取的agent: {used_agents_str}")
                elif bot_instance and hasattr(bot_instance, 'log_items'):
                    # 从CoordinatorBot的log_items获取（向后兼容）
                    log_items = bot_instance.log_items
                    if isinstance(log_items, list) and log_items:
                        # 从log_items中提取agent名称
                        agent_names = set()
                        for log_item in log_items:
                            if "Called tool:" in log_item:
                                for agent in KNOWN_AGENTS:
                                    if agent in log_item:
                                        agent_names.add(agent)
                        if agent_names:
                            used_agents_str = "coordinator_bot," + ",".join(sorted(agent_names))
                            logger.info(f"从log_items提取的agent: {used_agents_str}")

                # 获取Tool Agent的详细执行日志 - 统一的日志收集机制
                enhanced_logs = logs if logs else ""
                try:
                    # 优先使用AgentToolRegistry的聚合日志
                    if bot_instance and hasattr(bot_instance, 'tool_registry'):
                        if hasattr(bot_instance.tool_registry, 'get_aggregated_tool_logs'):
                            tool_agent_logs = bot_instance.tool_registry.get_aggregated_tool_logs()
                            if tool_agent_logs and tool_agent_logs.strip():
                                if enhanced_logs:
                                    enhanced_logs += "\n\n=== Tool Agent详细日志 ===\n" + tool_agent_logs
                                else:
                                    enhanced_logs = tool_agent_logs
                                logger.info(f"已添加Tool Agent详细日志，总长度: {len(enhanced_logs)}")

                    # 回退到CoordinatorBot的log_items（向后兼容）
                    elif bot_instance and hasattr(bot_instance, 'log_items'):
                        log_items = bot_instance.log_items
                        if isinstance(log_items, list) and log_items:
                            coordinator_logs = "\n".join(log_items)
                            if coordinator_logs.strip():
                                if enhanced_logs:
                                    enhanced_logs += "\n\n=== Coordinator执行日志 ===\n" + coordinator_logs
                                else:
                                    enhanced_logs = coordinator_logs
                                logger.info(f"已添加Coordinator执行日志，总长度: {len(enhanced_logs)}")
                    else:
                        logger.info("bot_instance不可用或没有日志收集方法，跳过详细日志收集")
                except Exception as e:
                    logger.warning(f"获取详细执行日志时出错: {e}")
                
                save_assistant_message(
                    username=user_name,
                    email=email,
                    conversation_id=conversation_id,
                    content=response,
                    timestamp=timestamp,
                    logs=enhanced_logs if enhanced_logs else None,
                    output_as_input=output_as_input_json,
                    agent=used_agents_str
                )
                logger.debug(f"助手回复已保存 (Convo ID: {conversation_id})，包含Tool Agent日志")
                
        except Exception as e:
            logger.error(f"保存助手消息时出错: {e}")

    async def execute_agent_stream(self, bot: CoordinatorBot, messages: List[Dict],
                                 user_obj: UserInfo, message_queue: queue.Queue) -> tuple:
        """执行Agent流式处理的核心逻辑"""
        for attempt in range(1, MAX_RETRIES + 1):
            # 重置agent追踪，开始新的查询
            bot.tool_registry.reset_agent_tracking()
            agent = bot.create_agent(LITE_LLM_MODEL)

            total_messages = bot.get_user_realtime_instruction_as_message_object() + messages

            logger.info(f"第 {attempt} 次尝试处理查询")

            # 启动流式推理
            result = Runner.run_streamed(
                agent,
                input=total_messages,
                max_turns=20,
                run_config=RunConfig(
                    model_settings=ModelSettings(temperature=0.1)
                ),
                context=user_obj,
            )

            # 处理流事件
            collected_logs = ""
            tool_calls_detected = False
            agent_execution_logs = []
            used_agent_names = []

            try:
                # 调用子类实现的流事件处理
                stream_result = await self.process_stream_events(result, message_queue, user_obj)
                collected_logs, tool_calls_detected, agent_execution_logs, used_agent_names = stream_result

            except Exception as e:
                logger.exception(f"流事件处理出错: {e}")
                if "Event loop is closed" in str(e):
                    message_queue.put({"type": "error", "content": "事件循环已关闭"})
                    break
                raise

            # 检测执行成功（Agent as Tool架构）
            if tool_calls_detected or len(collected_logs.strip()) > 0:
                success_msg = f"第 {attempt} 次尝试成功"
                if agent_execution_logs:
                    success_msg += f"，调用了 {len(agent_execution_logs)} 个专业工具"
                logger.info(success_msg)
                final_input_list = result.to_input_list()
                # 将bot对象和使用的agent名称传递给外部
                message_queue.put({"type": "bot_instance", "data": bot})
                message_queue.put({"type": "used_agents", "data": used_agent_names})
                message_queue.put({"type": "final_result", "data": final_input_list})
                return final_input_list, bot, used_agent_names
            else:
                # 没有工具调用且没有实质性输出，可能需要重试
                logger.warning(f"第 {attempt} 次尝试未检测到有效的工具调用或输出")
                if attempt < MAX_RETRIES:
                    message_queue.put({"type": "info", "content": "🤖未检测到有效响应，重试中..."})
                    continue
                else:
                    logger.error(f"所有 {MAX_RETRIES} 次尝试都未产生有效输出")
                    # 即使失败也要传递bot实例，以便获取日志
                    message_queue.put({"type": "bot_instance", "data": bot})
                    message_queue.put({"type": "used_agents", "data": used_agent_names})
                    message_queue.put({"type": "final_result", "data": None})
                    return None, bot, used_agent_names

        return None, None, []

    def create_async_worker(self, request: QueryRequest, message_queue: queue.Queue):
        """创建异步工作函数"""
        def async_worker():
            loop = None
            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # 运行协程
                loop.run_until_complete(self._process_stream_async(request, message_queue))

            except Exception as e:
                logger.exception(f"异步工作线程出错: {e}")
                message_queue.put({"type": "error", "content": str(e)})
                message_queue.put({"type": "final_result", "data": None})
            finally:
                # 确保事件循环被正确关闭
                if loop and not loop.is_closed():
                    try:
                        # 取消所有待处理的任务
                        pending = asyncio.all_tasks(loop)
                        for task in pending:
                            task.cancel()

                        # 等待任务取消完成
                        if pending:
                            loop.run_until_complete(
                                asyncio.gather(*pending, return_exceptions=True)
                            )

                        loop.close()
                        logger.info("Event loop closed properly")
                    except Exception as e:
                        logger.error(f"Error closing event loop: {e}")

                message_queue.put(None)  # 结束标记

        return async_worker

    async def _process_stream_async(self, request: QueryRequest, message_queue: queue.Queue):
        """处理流事件的协程"""
        try:
            # 获取历史
            history = self.get_conversation_history(request.user_info, request.conversation_id)

            # 构建消息
            messages = self.build_messages(request.user_query, request.images, history)

            # 创建用户对象
            user_obj = self.create_user_info_object(
                request.user_info, request.access_token, request.conversation_id
            )

            # 创建Bot
            bot = CoordinatorBot(request.user_info)

            # 执行Agent流式处理
            await self.execute_agent_stream(bot, messages, user_obj, message_queue)

        except Exception as e:
            logger.exception(f"处理流时出错: {e}")
            # 即使异常也要尝试传递bot实例（如果存在的话）
            try:
                if 'bot' in locals():
                    message_queue.put({"type": "bot_instance", "data": bot})
                if 'used_agent_names' in locals():
                    message_queue.put({"type": "used_agents", "data": used_agent_names})
            except Exception as inner_e:
                logger.warning(f"传递bot实例时出错: {inner_e}")
            message_queue.put({"type": "error", "content": str(e)})
            message_queue.put({"type": "final_result", "data": None})

    def handle_client_disconnect(self, request: QueryRequest):
        """处理客户端断开连接的情况"""
        try:
            # 提交后台任务继续执行
            BACKGROUND_MANAGER.submit_task({
                "processor": self,
                "request": request
            })
            logger.info(f"任务已提交到后台队列 (Convo ID: {request.conversation_id})")
        except Exception as e:
            logger.error(f"提交后台任务时出错: {e}")

    @staticmethod
    def shutdown_gracefully():
        """优雅关闭所有资源"""
        logger.info("开始优雅关闭BaseQueryProcessor资源...")

        try:
            # 关闭后台任务管理器
            BACKGROUND_MANAGER.shutdown()
            logger.info("后台任务管理器已关闭")
        except Exception as e:
            logger.error(f"关闭后台任务管理器时出错: {e}")

        logger.info("BaseQueryProcessor资源优雅关闭完成")


# 注册优雅关闭函数
import atexit
atexit.register(BaseQueryProcessor.shutdown_gracefully)
