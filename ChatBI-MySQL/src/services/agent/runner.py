"""
重构后的查询运行器 - 基于 message_queue 的统一架构
采用 DDD 原则，将复杂逻辑分解为独立的领域服务
"""
import asyncio
import json
import queue
import concurrent.futures
from datetime import datetime
from typing import List, Dict, Generator, Optional, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod

from agents import Runner, RunConfig, ModelSettings
from src.models.user_info_class import UserInfo
from src.services.agent.bots.coordinator_bot import CoordinatorBot
from src.services.agent.utils.formatter import format_event_message

from src.services.chatbot.history_service import (
    save_assistant_message,
    get_conversation_history_as_input_list
)
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email
from src.utils.system_monitor import start_system_monitoring, stop_system_monitoring, log_current_usage
from src.utils.image_utils import process_images_for_ai

# 领域常量
KNOWN_AGENTS = {
    "sales_order_analytics",
    "warehouse_and_fulfillment",
    "general_chat_bot"
}

MAX_RETRIES = 3
QUEUE_TIMEOUT = 600
THREAD_POOL_SIZE = 20

# 全局线程池 - 避免频繁创建销毁线程
THREAD_POOL = concurrent.futures.ThreadPoolExecutor(
    max_workers=THREAD_POOL_SIZE,
    thread_name_prefix="agent_worker"
)


# ==================== 领域模型 ====================

@dataclass
class QueryRequest:
    """查询请求领域模型"""
    user_query: str
    user_info: Dict[str, Any]
    access_token: Optional[str] = None
    conversation_id: Optional[str] = None
    images: Optional[List[str]] = None
    model_name: Optional[str] = None


@dataclass
class QueryResult:
    """查询结果领域模型"""
    response: str
    logs: str
    timestamp: int
    final_input_list: Optional[List[Dict]] = None
    bot_instance: Optional[CoordinatorBot] = None
    used_agents: Optional[List[str]] = None
    interrupted: bool = False


@dataclass
class StreamEvent:
    """流事件领域模型"""
    event_type: str
    content: str
    data: Optional[Any] = None


@dataclass
class StreamExecutionResult:
    """流执行结果"""
    success: bool
    response: str
    logs: str
    used_agents: List[str]


# ==================== 领域服务接口 ====================

class MessageQueueService(ABC):
    """消息队列服务接口"""

    @abstractmethod
    def put_event(self, event: StreamEvent) -> None:
        """发送事件到队列"""
        pass

    @abstractmethod
    def get_event(self, timeout: int = QUEUE_TIMEOUT) -> Optional[StreamEvent]:
        """从队列获取事件"""
        pass

    @abstractmethod
    def put_final_result(self, result: QueryResult) -> None:
        """发送最终结果"""
        pass

class QueueMessageService(MessageQueueService):
    """基于 queue.Queue 的消息队列服务实现"""

    def __init__(self):
        self._queue = queue.Queue()

    def put_event(self, event: StreamEvent) -> None:
        """发送事件到队列"""
        self._queue.put({
            "type": event.event_type,
            "content": event.content,
            "data": event.data
        })

    def get_event(self, timeout: int = QUEUE_TIMEOUT) -> Optional[StreamEvent]:
        """从队列获取事件"""
        try:
            message = self._queue.get(timeout=timeout)
            if message is None:
                return None
            return StreamEvent(
                event_type=message.get("type", "unknown"),
                content=message.get("content", ""),
                data=message.get("data")
            )
        except queue.Empty:
            logger.warning("消息队列获取超时")
            return None

    def put_final_result(self, result: QueryResult) -> None:
        """发送最终结果"""
        self._queue.put({"type": "final_result", "data": result.final_input_list})
        self._queue.put({"type": "bot_instance", "data": result.bot_instance})
        self._queue.put({"type": "used_agents", "data": result.used_agents})
        self._queue.put(None)  # 结束标记


class AgentExecutionService:
    """Agent 执行服务 - 负责协调 Agent 的执行"""

    def __init__(self, message_service: MessageQueueService):
        self.message_service = message_service

    async def execute_query(self, request: QueryRequest) -> QueryResult:
        """执行查询请求"""
        user_obj = self._create_user_object(request)

        # 获取历史消息
        history = self._get_conversation_history(request)

        # 构建消息
        messages = self._build_messages(request, history)

        # 执行 Agent 查询
        return await self._execute_with_retry(messages, user_obj, request)

    def _create_user_object(self, request: QueryRequest) -> UserInfo:
        """创建用户对象"""
        user_name = request.user_info.get("name")
        email = get_valid_user_email(request.user_info)
        union_id = request.user_info.get("union_id")
        summerfarm_api_token = request.user_info.get("summerfarm_api_token")

        return UserInfo(
            user_name=user_name,
            email=email,
            access_token=request.access_token,
            union_id=union_id,
            summerfarm_api_token=summerfarm_api_token,
            open_id=request.user_info.get("open_id", ""),
            conversation_id=request.conversation_id
        )

    def _get_conversation_history(self, request: QueryRequest) -> List[Dict]:
        """获取对话历史"""
        if not request.conversation_id:
            return []

        user_name = request.user_info.get("name")
        email = get_valid_user_email(request.user_info)

        history = get_conversation_history_as_input_list(
            user_name, email, request.conversation_id
        )
        logger.info(f"获取到 {len(history)} 条历史消息")
        return history

    def _build_messages(self, request: QueryRequest, history: List[Dict]) -> List[Dict]:
        """构建消息列表，支持多模态"""
        if request.images and len(request.images) > 0:
            logger.info(f"开始处理 {len(request.images)} 张图片")

            # 处理图片
            processed_images = process_images_for_ai(request.images)

            if processed_images:
                # 多模态消息格式
                content_parts = [{"type": "input_text", "text": request.user_query}]

                for base64_image in processed_images:
                    content_parts.append({
                        "type": "input_image",
                        "image_url": base64_image,
                        "detail": "auto"
                    })

                user_message = {
                    "role": "user",
                    "content": content_parts
                }
                logger.info(f"用户消息包含 {len(processed_images)} 张已处理的图片")
            else:
                logger.warning("所有图片处理失败，回退到纯文本消息")
                user_message = {"role": "user", "content": request.user_query}
        else:
            # 纯文本消息
            user_message = {"role": "user", "content": request.user_query}

        return history + [user_message]

    async def _execute_with_retry(self, messages: List[Dict], user_obj: UserInfo, request: QueryRequest) -> QueryResult:
        """带重试的执行逻辑"""

        for attempt in range(1, MAX_RETRIES + 1):
            try:
                bot = CoordinatorBot(request.user_info)
                agent = bot.create_agent()

                total_messages = bot.get_user_realtime_instruction_as_message_object() + messages

                logger.info(f"第 {attempt} 次尝试处理查询")

                # 启动流式推理
                result = Runner.run_streamed(
                    agent,
                    input=total_messages,
                    max_turns=20,
                    run_config=RunConfig(
                        model_settings=ModelSettings(**bot.get_model_settings())
                    ),
                    context=user_obj,
                )

                # 处理流事件
                execution_result = await self._process_stream_events(result)

                if execution_result.success:
                    logger.info(f"第 {attempt} 次尝试成功")
                    return QueryResult(
                        response=execution_result.response,
                        logs=execution_result.logs,
                        timestamp=int(datetime.now().timestamp() * 1000),
                        final_input_list=result.to_input_list(),
                        bot_instance=bot,
                        used_agents=execution_result.used_agents
                    )
                else:
                    logger.warning(f"第 {attempt} 次尝试未检测到有效输出")
                    if attempt < MAX_RETRIES:
                        self.message_service.put_event(
                            StreamEvent("info", "🤖未检测到有效响应，重试中...")
                        )
                        continue
                    else:
                        logger.error(f"所有 {MAX_RETRIES} 次尝试都未产生有效输出")
                        return QueryResult(
                            response="",
                            logs=execution_result.logs,
                            timestamp=int(datetime.now().timestamp() * 1000),
                            final_input_list=None,
                            bot_instance=bot,
                            used_agents=execution_result.used_agents
                        )

            except Exception as e:
                logger.exception(f"第 {attempt} 次尝试执行出错: {e}")
                if attempt == MAX_RETRIES:
                    raise
                continue

    async def _process_stream_events(self, result) -> StreamExecutionResult:
        """处理流事件"""
        collected_logs = ""
        tool_calls_detected = False
        agent_execution_logs = []
        used_agent_names = []

        try:
            async for event in result.stream_events():
                message = format_event_message(event)
                if message:
                    msg_type = message.get("type")
                    content = message.get("content", "")

                    # 收集所有相关日志
                    if msg_type in ["log", "handoff_log", "tool_call_log", "tool_output"] and content:
                        collected_logs += str(content) + "\n"

                    # 检测工具调用（Agent as Tool架构的关键指标）
                    if msg_type == "tool_call_log" and content:
                        agent_name = self._extract_agent_name_from_tool_call(content)
                        if agent_name and agent_name in KNOWN_AGENTS:
                            if agent_name not in used_agent_names:
                                tool_calls_detected = True
                                used_agent_names.append(agent_name)

                            agent_log = f"🔄 CoordinatorBot调用专业工具: {agent_name}"
                            agent_execution_logs.append(agent_log)
                            logger.info(agent_log)

                            # 发送类似handoff的日志消息
                            self.message_service.put_event(
                                StreamEvent("handoff_log", agent_log)
                            )

                    # 发送事件到消息队列
                    self.message_service.put_event(
                        StreamEvent(msg_type, content, message.get("data"))
                    )

        except Exception as e:
            logger.exception(f"流事件处理出错: {e}")
            if "Event loop is closed" in str(e):
                self.message_service.put_event(
                    StreamEvent("error", "事件循环已关闭")
                )
                return StreamExecutionResult(False, "", collected_logs, used_agent_names)
            raise

        # 检测执行成功
        success = tool_calls_detected or len(collected_logs.strip()) > 0
        success_msg = ""
        if success and agent_execution_logs:
            success_msg = f"调用了 {len(agent_execution_logs)} 个专业工具"

        return StreamExecutionResult(success, success_msg, collected_logs, used_agent_names)

    def _extract_agent_name_from_tool_call(self, tool_call: str) -> str:
        """从工具调用中提取Agent名称"""
        try:
            import re
            name_match = re.search(r"name='([^']*)'", tool_call)
            return name_match.group(1) if name_match else "unknown"
        except Exception as e:
            logger.exception(f"提取Agent名称时出错: {e}")
            return "unknown"


class DataPersistenceService:
    """数据持久化服务 - 负责保存查询结果"""

    @staticmethod
    def save_query_result(result: QueryResult, request: QueryRequest) -> None:
        """保存查询结果到数据库"""
        if not (result.response or result.logs):
            return

        try:
            user_name = request.user_info.get("name")
            email = get_valid_user_email(request.user_info)

            # 构建结构化消息
            structured_message = None
            output_as_input_json = None

            if (result.final_input_list and
                isinstance(result.final_input_list, list) and
                len(result.final_input_list) > 0):

                last_message = result.final_input_list[-1]
                if (isinstance(last_message, dict) and
                    last_message.get("role") == "assistant"):
                    structured_message = last_message

            if structured_message:
                try:
                    output_as_input_json = json.dumps(
                        structured_message,
                        ensure_ascii=False,
                        indent=2
                    )
                except Exception as e:
                    logger.error(f"序列化结构化消息失败: {e}")

            # 获取使用的agent信息
            used_agents_str = None
            if result.used_agents and len(result.used_agents) > 0:
                used_agents_str = ",".join(result.used_agents)
                logger.info(f"本次查询使用的agent: {used_agents_str}")
            elif result.bot_instance and hasattr(result.bot_instance, 'log_items'):
                used_agents_str = ",".join(result.bot_instance.log_items)
                logger.info(f"从bot_instance获取的agent: {used_agents_str}")

            # 获取增强的日志
            enhanced_logs = result.logs if result.logs else ""
            try:
                if result.bot_instance and hasattr(result.bot_instance, 'log_items'):
                    log_items = result.bot_instance.log_items
                    if isinstance(log_items, list):
                        enhanced_logs += "\n".join(log_items)
                        logger.info(f"已添加Tool Agent详细日志，总长度: {len(enhanced_logs)}")
            except Exception as e:
                logger.exception(f"获取Tool Agent日志时出错: {e}")

            # 保存到数据库
            if request.conversation_id and result.timestamp:
                save_assistant_message(
                    username=user_name,
                    email=email,
                    conversation_id=request.conversation_id,
                    content=result.response,
                    timestamp=result.timestamp,
                    logs=enhanced_logs if enhanced_logs else None,
                    output_as_input=output_as_input_json,
                    agent=used_agents_str
                )
                logger.debug(f"助手回复已保存 (Convo ID: {request.conversation_id})")

        except Exception as e:
            logger.error(f"保存助手消息时出错: {e}")


class BackgroundTaskManager:
    """后台任务管理器 - 处理中断的查询任务"""

    def __init__(self, max_workers=10):
        self.task_queue = queue.Queue()
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="background_worker"
        )
        self._start_workers()

    def _start_workers(self):
        """启动后台工作线程"""
        for _ in range(3):
            self.executor.submit(self._worker)

    def _worker(self):
        """后台工作线程"""
        while True:
            try:
                task = self.task_queue.get(timeout=30)
                if task is None:
                    break

                self._process_task(task)

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Background worker error: {e}")
            finally:
                self.task_queue.task_done()

    def _process_task(self, task_data: Dict[str, Any]):
        """处理后台任务"""
        try:
            request = QueryRequest(
                user_query=task_data["user_query"],
                user_info=task_data["user_info"],
                access_token=task_data["access_token"],
                conversation_id=task_data["conversation_id"],
                model_name=task_data.get("model_name")
            )

            # 执行任务并消费结果
            for _ in run_agent_query(
                request.user_query, request.user_info, request.access_token,
                request.conversation_id, None, request.model_name
            ):
                pass

            logger.info(f"后台任务完成 (Convo ID: {request.conversation_id})")

        except Exception as e:
            logger.error(f"Background task processing error: {e}")

    def submit_task(self, task_data: Dict[str, Any]):
        """提交后台任务"""
        self.task_queue.put(task_data)


class AsyncQueryExecutor:
    """异步查询执行器 - 负责在独立线程中执行查询"""

    def __init__(self, execution_service: AgentExecutionService, message_service: MessageQueueService):
        self.execution_service = execution_service
        self.message_service = message_service

    def execute_async(self, request: QueryRequest) -> None:
        """在线程池中异步执行查询"""
        THREAD_POOL.submit(self._async_worker, request)

    def _async_worker(self, request: QueryRequest) -> None:
        """异步工作函数 - 使用按需创建的事件循环"""
        loop = None
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 运行协程
            loop.run_until_complete(self._process_query(request))

        except Exception as e:
            logger.exception(f"异步工作线程出错: {e}")
            self.message_service.put_event(StreamEvent("error", str(e)))
            self.message_service.put_final_result(QueryResult(
                response="", logs="", timestamp=int(datetime.now().timestamp() * 1000)
            ))
        finally:
            # 确保事件循环被正确关闭
            if loop and not loop.is_closed():
                try:
                    # 取消所有待处理的任务
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()

                    # 等待任务取消完成
                    if pending:
                        loop.run_until_complete(
                            asyncio.gather(*pending, return_exceptions=True)
                        )

                    loop.close()
                    logger.info("Event loop closed properly")
                except Exception as e:
                    logger.error(f"Error closing event loop: {e}")

    async def _process_query(self, request: QueryRequest) -> None:
        """处理查询的协程"""
        try:
            result = await self.execution_service.execute_query(request)
            self.message_service.put_final_result(result)
        except Exception as e:
            logger.exception(f"处理查询时出错: {e}")
            self.message_service.put_event(StreamEvent("error", str(e)))
            self.message_service.put_final_result(QueryResult(
                response="", logs="", timestamp=int(datetime.now().timestamp() * 1000)
            ))


class StreamResponseGenerator:
    """流响应生成器 - 负责生成客户端响应"""

    def __init__(self, message_service: MessageQueueService, request: QueryRequest):
        self.message_service = message_service
        self.request = request

    def generate(self) -> Generator:
        """生成流响应"""
        full_assistant_response = ""
        full_assistant_logs = ""
        assistant_timestamp = None
        final_result = None
        interrupted = False
        bot_instance = None
        used_agents_list = []

        try:
            while True:
                event = self.message_service.get_event()

                if event is None:
                    # 处理最终结果和保存逻辑
                    self._handle_final_result(
                        final_result, full_assistant_response, full_assistant_logs,
                        assistant_timestamp, used_agents_list, bot_instance
                    )
                    break

                # 处理特殊事件类型
                if event.event_type == "final_result":
                    final_result = event.data
                    continue
                elif event.event_type == "bot_instance":
                    bot_instance = event.data
                    continue
                elif event.event_type == "used_agents":
                    used_agents_list = event.data or []
                    continue

                # 设置时间戳
                if not assistant_timestamp and event.event_type != "error":
                    assistant_timestamp = int(datetime.now().timestamp() * 1000)

                # 累积内容
                if event.event_type == "data":
                    full_assistant_response += event.content
                elif event.event_type not in ["data", "final_result"] and event.content:
                    log_line = f"[{event.event_type.upper()}] {event.content}"
                    full_assistant_logs += log_line + "\n"

                # 发送到客户端
                yield "[data]:" + json.dumps({
                    "type": event.event_type,
                    "content": event.content,
                    "data": event.data
                }, ensure_ascii=False) + "\n"

        except (GeneratorExit, ConnectionResetError, BrokenPipeError) as e:
            interrupted = True
            logger.warning(f"客户端断开连接: {e}")
        except Exception as e:
            logger.exception(f"生成器错误: {e}")
        finally:
            if interrupted:
                # 提交后台任务
                BACKGROUND_MANAGER.submit_task({
                    "user_query": self.request.user_query,
                    "user_info": self.request.user_info,
                    "access_token": self.request.access_token,
                    "conversation_id": self.request.conversation_id,
                    "model_name": self.request.model_name,
                })
                logger.info(f"任务已提交到后台队列 (Convo ID: {self.request.conversation_id})")

    def _handle_final_result(self, final_input_list, response, logs, timestamp,
                           used_agents_list, bot_instance):
        """处理最终结果和保存"""
        if not (response or logs):
            return

        result = QueryResult(
            response=response,
            logs=logs,
            timestamp=timestamp,
            final_input_list=final_input_list,
            bot_instance=bot_instance,
            used_agents=used_agents_list
        )

        DataPersistenceService.save_query_result(result, self.request)


# 全局实例
BACKGROUND_MANAGER = BackgroundTaskManager()


def run_agent_query(
        user_query: str,
        user_info: dict = {},
        access_token: str = None,
        conversation_id: str = None,
        images: list = None,
        model_name: str = None,
) -> Generator:
    """
    重构后的查询处理函数 - 基于 message_queue 的统一架构
    """
    # 创建领域对象
    request = QueryRequest(
        user_query=user_query,
        user_info=user_info,
        access_token=access_token,
        conversation_id=conversation_id,
        images=images,
        model_name=model_name
    )

    # 创建服务实例
    message_service = QueueMessageService()
    execution_service = AgentExecutionService(message_service)

    # 创建异步执行器和响应生成器
    async_executor = AsyncQueryExecutor(execution_service, message_service)
    response_generator = StreamResponseGenerator(message_service, request)

    # 启动异步执行
    async_executor.execute_async(request)

    # 返回流响应生成器
    return response_generator.generate()





# 优雅关闭函数
def shutdown_gracefully():
    """优雅关闭所有资源"""
    logger.info("开始优雅关闭...")

    # 记录关闭前的资源使用情况
    log_current_usage()

    # 关闭线程池
    THREAD_POOL.shutdown(wait=True)

    # 关闭后台任务管理器
    BACKGROUND_MANAGER.task_queue.put(None)
    BACKGROUND_MANAGER.executor.shutdown(wait=True)

    # 停止系统监控
    stop_system_monitoring()

    # 记录关闭后的资源使用情况
    log_current_usage()

    logger.info("优雅关闭完成")

# 启动系统监控
start_system_monitoring()

# 在应用退出时调用
import atexit
atexit.register(shutdown_gracefully)