"""
API查询处理器
继承BaseQueryProcessor，为API接口提供统一的查询处理功能
"""
import asyncio
import json
import queue
from datetime import datetime
from typing import Generator

from src.services.agent.base_query_processor import (
    BaseQueryProcessor, QueryRequest, THREAD_POOL, BACKGROUND_MANAGER
)
from src.utils.logger import logger


class APIQueryProcessor(BaseQueryProcessor):
    """API查询处理器 - 继承BaseQueryProcessor复用核心逻辑"""
    
    def __init__(self):
        super().__init__(THREAD_POOL)
    
    async def process_stream_events(self, result, message_queue: queue.Queue, user_obj) -> tuple:
        """API流事件处理逻辑 - 复用基类的统一处理"""
        # 直接使用基类的统一流事件处理逻辑
        return await super().process_stream_events(result, message_queue, user_obj)
    
    def handle_message_output(self, message: dict) -> str:
        """实现API特有的消息输出格式"""
        return message.get("content", "")
    
    def run_query(self, user_query: str, user_info: dict = {}, access_token: str = None,
                  conversation_id: str = None, images: list = None, model_name: str = None) -> Generator:
        """
        API查询处理的主入口函数
        
        Args:
            user_query: 用户查询
            user_info: 用户信息字典
            access_token: 访问令牌
            conversation_id: 对话ID
            images: 图片列表
            model_name: 模型名称
            
        Returns:
            Generator: 流式响应生成器
        """
        # 创建查询请求对象
        request = QueryRequest(
            user_query=user_query,
            user_info=user_info,
            access_token=access_token,
            conversation_id=conversation_id,
            images=images,
            model_name=model_name
        )
        
        # 创建消息队列
        message_queue = queue.Queue()
        
        # 创建异步工作函数
        async_worker = self.create_async_worker(request, message_queue)
        
        # 在线程池中执行异步工作
        self.thread_pool.submit(async_worker)
        
        # 返回流响应生成器
        return self._generate_api_response(request, message_queue)
    
    def _generate_api_response(self, request: QueryRequest, message_queue: queue.Queue) -> Generator:
        """生成API流式响应"""
        full_assistant_response = ""
        full_assistant_logs = ""
        assistant_timestamp = None
        final_input_list_from_worker = None
        interrupted = False
        bot_instance = None
        used_agents_list = []
        
        try:
            while True:
                try:
                    # 设置超时避免无限等待
                    message = message_queue.get(timeout=600)
                except queue.Empty:
                    logger.warning("消息队列获取超时")
                    # 通知用户超时情况
                    yield "[data]:" + json.dumps({"type": "error", "content": "响应超时，请稍后重试"}, ensure_ascii=False) + "\n"
                    break
                
                if message is None:
                    # 处理最终结果和保存逻辑
                    self._handle_final_result(
                        final_input_list_from_worker,
                        full_assistant_response,
                        full_assistant_logs,
                        assistant_timestamp,
                        used_agents_list,
                        bot_instance,
                        request
                    )
                    break
                
                if isinstance(message, dict):
                    msg_type = message.get("type")
                    
                    if msg_type == "final_result":
                        final_input_list_from_worker = message.get("data")
                        continue
                    
                    if msg_type == "bot_instance":
                        bot_instance = message.get("data")
                        continue
                    
                    if msg_type == "used_agents":
                        used_agents_list = message.get("data", [])
                        continue
                    
                    if not assistant_timestamp and msg_type != "error":
                        assistant_timestamp = int(datetime.now().timestamp() * 1000)
                    
                    # 累积内容
                    if msg_type == "data":
                        full_assistant_response += message.get("content", "")
                    elif msg_type not in ["data", "final_result"] and message.get("content"):
                        log_line = f"[{msg_type.upper()}] {message.get('content', '')}"
                        full_assistant_logs += log_line + "\n"
                    
                    # 发送到客户端
                    yield "[data]:" + json.dumps(message, ensure_ascii=False) + "\n"
                    
        except (GeneratorExit, ConnectionResetError, BrokenPipeError) as e:
            interrupted = True
            logger.warning(f"API客户端断开连接: {e}")
        except Exception as e:
            logger.exception(f"API生成器错误: {e}")
        finally:
            if interrupted:
                # 处理客户端断开连接
                self.handle_client_disconnect(request)
    
    def _handle_final_result(self, final_input_list, response, logs, timestamp,
                           used_agents_list, bot_instance, request: QueryRequest):
        """处理最终结果和保存"""
        if not (response or logs):
            return
        
        try:
            if timestamp:
                # 使用基类的统一保存方法
                self.save_assistant_response(
                    request.user_info, request.conversation_id, response, logs,
                    timestamp, final_input_list, used_agents_list, bot_instance
                )
                logger.info(f"API查询结果已保存到对话 {request.conversation_id}")
                
        except Exception as e:
            logger.error(f"保存API查询结果时出错: {e}")


# 创建全局API查询处理器实例
api_processor = APIQueryProcessor()


def run_agent_query(user_query: str, user_info: dict = {}, access_token: str = None,
                   conversation_id: str = None, images: list = None, model_name: str = None) -> Generator:
    """
    API查询处理的兼容性函数，保持与原runner.py的接口一致
    
    Args:
        user_query: 用户查询
        user_info: 用户信息字典
        access_token: 访问令牌
        conversation_id: 对话ID
        images: 图片列表
        model_name: 模型名称
        
    Returns:
        Generator: 流式响应生成器
    """
    return api_processor.run_query(
        user_query, user_info, access_token, conversation_id, images, model_name
    )


# 优雅关闭函数
def shutdown_gracefully():
    """优雅关闭所有资源"""
    logger.info("开始优雅关闭API查询处理器...")
    BaseQueryProcessor.shutdown_gracefully()
    logger.info("API查询处理器优雅关闭完成")


# 注册优雅关闭函数
import atexit
atexit.register(shutdown_gracefully)
