#!/usr/bin/env python3
"""
重构验证测试脚本
测试重构后的飞书消息处理功能，确保功能完整性和性能优化效果
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import logger
from src.services.agent.base_query_processor import BaseQueryProcessor, QueryRequest, KNOWN_AGENTS
from src.services.agent.api_query_processor import APIQueryProcessor
from src.services.feishu.query_processor import FeishuQueryProcessor


def test_base_query_processor():
    """测试BaseQueryProcessor基类功能"""
    print("🧪 测试BaseQueryProcessor基类功能...")
    
    try:
        # 测试创建实例
        processor = APIQueryProcessor()
        assert processor is not None, "APIQueryProcessor实例创建失败"
        
        # 测试QueryRequest数据模型
        request = QueryRequest(
            user_query="测试查询",
            user_info={"name": "test_user", "email": "<EMAIL>"},
            conversation_id="test_conv_123"
        )
        assert request.user_query == "测试查询", "QueryRequest创建失败"
        
        # 测试用户信息对象创建
        user_obj = processor.create_user_info_object(
            {"name": "test_user", "email": "<EMAIL>"},
            conversation_id="test_conv_123"
        )
        assert user_obj.user_name == "test_user", "UserInfo对象创建失败"
        
        # 测试消息构建
        messages = processor.build_messages("测试查询", history=[])
        assert len(messages) == 1, "消息构建失败"
        assert messages[0]["role"] == "user", "用户消息角色错误"
        
        # 测试Agent名称提取
        agent_name = processor.extract_agent_name_from_tool_output(
            '{"agent_name": "sales_order_analytics", "success": true}'
        )
        assert agent_name == "sales_order_analytics", "Agent名称提取失败"
        
        print("✅ BaseQueryProcessor基类功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ BaseQueryProcessor基类功能测试失败: {e}")
        return False


def test_feishu_query_processor():
    """测试FeishuQueryProcessor功能"""
    print("🧪 测试FeishuQueryProcessor功能...")
    
    try:
        # 测试创建实例
        processor = FeishuQueryProcessor()
        assert processor is not None, "FeishuQueryProcessor实例创建失败"
        
        # 测试继承关系
        assert isinstance(processor, BaseQueryProcessor), "继承关系错误"
        
        # 测试消息输出处理
        message = {"type": "data", "content": "测试内容"}
        output = processor.handle_message_output(message)
        assert output == "测试内容", "消息输出处理失败"
        
        print("✅ FeishuQueryProcessor功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ FeishuQueryProcessor功能测试失败: {e}")
        return False


def test_api_query_processor():
    """测试APIQueryProcessor功能"""
    print("🧪 测试APIQueryProcessor功能...")
    
    try:
        # 测试创建实例
        processor = APIQueryProcessor()
        assert processor is not None, "APIQueryProcessor实例创建失败"
        
        # 测试继承关系
        assert isinstance(processor, BaseQueryProcessor), "继承关系错误"
        
        # 测试消息输出处理
        message = {"type": "data", "content": "测试内容"}
        output = processor.handle_message_output(message)
        assert output == "测试内容", "消息输出处理失败"
        
        print("✅ APIQueryProcessor功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ APIQueryProcessor功能测试失败: {e}")
        return False


def test_agent_tracking():
    """测试Agent追踪机制"""
    print("🧪 测试Agent追踪机制...")
    
    try:
        processor = APIQueryProcessor()
        
        # 测试已知Agent列表
        assert "sales_order_analytics" in KNOWN_AGENTS, "已知Agent列表不完整"
        assert "warehouse_and_fulfillment" in KNOWN_AGENTS, "已知Agent列表不完整"
        assert "general_chat_bot" in KNOWN_AGENTS, "已知Agent列表不完整"
        
        # 测试从工具输出提取Agent名称
        test_cases = [
            ('{"agent_name": "sales_order_analytics"}', "sales_order_analytics"),
            ('{"results": [{"agent_name": "warehouse_and_fulfillment", "success": true}]}', "warehouse_and_fulfillment"),
            ('Tool called: general_chat_bot', "general_chat_bot"),  # 因为general_chat_bot在KNOWN_AGENTS中
            ('analysis completed', "专业分析工具"),
        ]
        
        for tool_output, expected in test_cases:
            result = processor.extract_agent_name_from_tool_output(tool_output)
            assert result == expected, f"Agent名称提取失败: {tool_output} -> {result} (期望: {expected})"
        
        print("✅ Agent追踪机制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Agent追踪机制测试失败: {e}")
        return False


def test_resource_management():
    """测试资源管理功能"""
    print("🧪 测试资源管理功能...")
    
    try:
        from src.services.agent.base_query_processor import BACKGROUND_MANAGER, THREAD_POOL
        
        # 测试线程池
        assert THREAD_POOL is not None, "全局线程池未初始化"
        assert not THREAD_POOL._shutdown, "线程池已关闭"
        
        # 测试后台任务管理器
        assert BACKGROUND_MANAGER is not None, "后台任务管理器未初始化"
        assert BACKGROUND_MANAGER.task_queue is not None, "任务队列未初始化"
        
        # 测试任务提交
        test_task = {"test": "data"}
        BACKGROUND_MANAGER.submit_task(test_task)
        
        print("✅ 资源管理功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 资源管理功能测试失败: {e}")
        return False


def test_backward_compatibility():
    """测试向后兼容性"""
    print("🧪 测试向后兼容性...")
    
    try:
        # 测试原有的run_agent_query函数是否可用
        from src.services.agent.api_query_processor import run_agent_query
        assert callable(run_agent_query), "run_agent_query函数不可用"
        
        # 测试QueryProcessor别名
        from src.services.feishu.query_processor import QueryProcessor
        assert QueryProcessor is not None, "QueryProcessor别名不可用"
        
        print("✅ 向后兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理机制"""
    print("🧪 测试错误处理机制...")
    
    try:
        processor = APIQueryProcessor()
        
        # 测试无效输入的处理
        try:
            processor.extract_agent_name_from_tool_output(None)
        except Exception:
            pass  # 应该优雅处理错误
        
        # 测试空查询请求
        request = QueryRequest(
            user_query="",
            user_info={},
            conversation_id=None
        )
        assert request.user_query == "", "空查询请求处理失败"
        
        print("✅ 错误处理机制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理机制测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始重构验证测试...")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    tests = [
        ("BaseQueryProcessor基类功能", test_base_query_processor),
        ("FeishuQueryProcessor功能", test_feishu_query_processor),
        ("APIQueryProcessor功能", test_api_query_processor),
        ("Agent追踪机制", test_agent_tracking),
        ("资源管理功能", test_resource_management),
        ("向后兼容性", test_backward_compatibility),
        ("错误处理机制", test_error_handling),
    ]
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        result = test_func()
        test_results.append((test_name, result))
    
    # 汇总测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed + failed} 个测试")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    
    if failed == 0:
        print("\n🎉 所有测试通过！重构成功！")
        return True
    else:
        print(f"\n⚠️  有 {failed} 个测试失败，需要修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
